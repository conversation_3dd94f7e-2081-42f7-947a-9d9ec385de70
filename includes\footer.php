    <!-- Footer -->
    <footer class="bg-dark text-white mt-5">
        <div class="container py-5">
            <div class="row">
                <!-- معلومات الموقع -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5 class="mb-3 text-white">
                        <i class="bi bi-shop"></i> <?php echo getSetting('site_name'); ?>
                    </h5>
                    <p class="text-white">
                        <?php echo getSetting('site_description'); ?>
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-white me-3 fs-4"><i class="bi bi-facebook"></i></a>
                        <a href="#" class="text-white me-3 fs-4"><i class="bi bi-twitter"></i></a>
                        <a href="#" class="text-white me-3 fs-4"><i class="bi bi-instagram"></i></a>
                        <a href="https://wa.me/<?php echo str_replace('+', '', getSetting('whatsapp_number')); ?>"
                           class="text-white me-3 fs-4" target="_blank">
                            <i class="bi bi-whatsapp"></i>
                        </a>
                    </div>
                </div>

                <!-- روابط سريعة -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="mb-3 text-white">روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="<?php echo SITE_URL; ?>" class="text-white text-decoration-none">الرئيسية</a></li>
                        <li class="mb-2"><a href="<?php echo SITE_URL; ?>/products.php" class="text-white text-decoration-none">المنتجات</a></li>
                        <li class="mb-2"><a href="<?php echo SITE_URL; ?>/offers.php" class="text-white text-decoration-none">العروض</a></li>
                        <li class="mb-2"><a href="<?php echo SITE_URL; ?>/guidelines.php" class="text-white text-decoration-none">الإرشادات</a></li>
                        <li class="mb-2"><a href="<?php echo SITE_URL; ?>/influencers.php" class="text-white text-decoration-none">المؤثرين</a></li>
                        <li class="mb-2"><a href="<?php echo SITE_URL; ?>/contact.php" class="text-white text-decoration-none">اتصل بنا</a></li>
                        <li class="mb-2"><a href="#" class="text-white text-decoration-none">سياسة الخصوصية</a></li>
                        <li class="mb-2"><a href="#" class="text-white text-decoration-none">الشروط والأحكام</a></li>
                        <li class="mb-2"><a href="#" class="text-white text-decoration-none">سياسة الإرجاع</a></li>
                    </ul>
                </div>
                
                <!-- التصنيفات -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3 text-white">التصنيفات</h6>
                    <ul class="list-unstyled">
                        <?php
                        $categories = fetchAll("SELECT id, name FROM categories ORDER BY name LIMIT 5");
                        if ($categories):
                            foreach ($categories as $category):
                        ?>
                            <li class="mb-2">
                                <a href="<?php echo SITE_URL; ?>/products.php?category=<?php echo $category['id']; ?>"
                                   class="text-white text-decoration-none">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </a>
                            </li>
                        <?php
                            endforeach;
                        endif;
                        ?>
                    </ul>
                </div>

                <!-- معلومات الاتصال -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="mb-3 text-white">تواصل معنا</h6>
                    <div class="contact-info">
                        <p class="mb-2 text-white">
                            <i class="bi bi-telephone me-2"></i>
                            <a href="tel:<?php echo getSetting('contact_phone'); ?>" class="text-white text-decoration-none">
                                <?php echo getSetting('contact_phone'); ?>
                            </a>
                        </p>
                        <p class="mb-2 text-white">
                            <i class="bi bi-envelope me-2"></i>
                            <a href="mailto:<?php echo getSetting('contact_email'); ?>" class="text-white text-decoration-none">
                                <?php echo getSetting('contact_email'); ?>
                            </a>
                        </p>
                        <p class="mb-3 text-white">
                            <i class="bi bi-whatsapp me-2"></i>
                            <a href="https://wa.me/<?php echo str_replace('+', '', getSetting('whatsapp_number')); ?>"
                               class="text-white text-decoration-none" target="_blank">
                                واتساب: <?php echo getSetting('whatsapp_number'); ?>
                            </a>
                        </p>
                    </div>

                    <!-- النشرة البريدية -->
                    <div class="newsletter">
                        <h6 class="mb-2 text-white">اشترك في النشرة البريدية</h6>
                        <form id="newsletterForm" class="d-flex">
                            <input type="email" class="form-control me-2" placeholder="بريدك الإلكتروني"
                                   name="email" required>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-send"></i>
                            </button>
                        </form>
                        <small class="text-white-50">احصل على آخر العروض والمنتجات الجديدة</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Copyright -->
        <div class="bg-secondary py-4">
            <div class="container">
                <div class="text-center">
                    <p class="mb-0 text-white">
                        &copy; <?php echo date('Y'); ?> <?php echo getSetting('site_name'); ?>.
                        جميع الحقوق محفوظة.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Hero Carousel JS (Homepage only) -->
    <?php if (basename($_SERVER['PHP_SELF']) == 'index.php'): ?>
    <script src="<?php echo SITE_URL; ?>/assets/js/hero-carousel.js"></script>
    <?php endif; ?>

    <!-- Custom JavaScript -->
    <script>
        // النشرة البريدية
        document.getElementById('newsletterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('<?php echo SITE_URL; ?>/ajax/newsletter.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم الاشتراك بنجاح في النشرة البريدية!');
                    this.reset();
                } else {
                    alert(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ، يرجى المحاولة مرة أخرى');
            });
        });

        // إضافة منتج إلى السلة
        function addToCart(productId, quantity = 1) {
            const formData = new FormData();
            formData.append('product_id', productId);
            formData.append('quantity', quantity);
            
            fetch('<?php echo SITE_URL; ?>/ajax/cart.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث عداد السلة
                    updateCartCount();
                    
                    // إظهار رسالة نجاح
                    showToast('تم إضافة المنتج إلى السلة بنجاح!', 'success');
                } else {
                    showToast(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
            });
        }

        // تحديث عداد السلة
        function updateCartCount() {
            fetch('<?php echo SITE_URL; ?>/ajax/cart_count.php')
            .then(response => response.json())
            .then(data => {
                const cartBadge = document.querySelector('.cart-badge');
                if (data.count > 0) {
                    if (cartBadge) {
                        cartBadge.textContent = data.count;
                    } else {
                        const cartIcon = document.querySelector('.cart-icon');
                        const badge = document.createElement('span');
                        badge.className = 'cart-badge';
                        badge.textContent = data.count;
                        cartIcon.appendChild(badge);
                    }
                } else if (cartBadge) {
                    cartBadge.remove();
                }
            });
        }

        // إظهار رسائل Toast
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toastContainer') || createToastContainer();
            
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            toastContainer.appendChild(toast);
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }

        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            container.style.zIndex = '1055';
            document.body.appendChild(container);
            return container;
        }

        // العودة إلى الأعلى
        window.addEventListener('scroll', function() {
            const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
            const backToTop = document.getElementById('backToTop');
            
            if (scrollTop > 300) {
                if (!backToTop) {
                    createBackToTopButton();
                }
            } else if (backToTop) {
                backToTop.remove();
            }
        });

        function createBackToTopButton() {
            const button = document.createElement('button');
            button.id = 'backToTop';
            button.className = 'back-to-top-btn position-fixed';
            button.style.cssText = `
                bottom: 30px;
                right: 30px;
                z-index: 1000;
                border-radius: 50%;
                width: 55px;
                height: 55px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                color: white;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;
                opacity: 0.9;
            `;
            button.innerHTML = '<i class="bi bi-chevron-up" style="font-size: 1.2rem; font-weight: bold;"></i>';
            button.onclick = () => window.scrollTo({ top: 0, behavior: 'smooth' });

            // Add hover effects
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.05)';
                this.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.4)';
                this.style.opacity = '1';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '0 4px 15px rgba(102, 126, 234, 0.3)';
                this.style.opacity = '0.9';
            });

            // RTL support
            if (document.documentElement.dir === 'rtl') {
                button.style.right = 'auto';
                button.style.left = '30px';
            }

            document.body.appendChild(button);
        }
    </script>
</body>
</html>
